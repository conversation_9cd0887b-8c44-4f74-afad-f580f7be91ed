package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.openapi.uml.PresentationElementsManager;
import com.nomagic.magicdraw.openapi.uml.ReadOnlyElementException;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.pmw790.power.functions.ConnectionRegistry;
import com.pmw790.power.functions.Utilities;

import java.util.*;
import static com.pmw790.power.functions.Utilities.Log;

/**
 * Context class holding information needed for room power diagram creation
 * This manages multiple cabinets within a room
 */
public class RoomDiagramContext {
    private final Project project;
    private final Class roomBlock;
    private final ConnectionRegistry registry;
    private final String roomName;
    private final List<String> cabinetNames;
    private final Map<String, CabinetDiagramContext> cabinetContexts;
    private final Set<String> cabinetsWithProviders;
    private final Map<String, List<String>> providerHierarchyCache;
    private final Map<String, Property> roomPowerProviderPropertiesCache;

    /**
     * Creates a new context for room diagram operations
     */
    public RoomDiagramContext(Project project, Class roomBlock, ConnectionRegistry registry) {
        this.project = project;
        this.roomBlock = roomBlock;
        this.registry = registry;
        this.roomName = roomBlock.getName();
        this.cabinetNames = registry.getCabinetsForRoom(roomName);
        this.cabinetContexts = new HashMap<>();
        this.cabinetsWithProviders = new HashSet<>();
        this.providerHierarchyCache = new HashMap<>();
        this.roomPowerProviderPropertiesCache = new HashMap<>();

        // Pre-cache provider hierarchy for faster access
        precacheProviderHierarchy();

        // Pre-identify cabinets with providers for faster filtering
        identifyCabinetsWithProviders();
    }

    /**
     * Pre-caches the provider hierarchy data for this room
     */
    private void precacheProviderHierarchy() {
        Map<String, Map<String, List<String>>> roomToProvidersMap = registry.getRoomToPowerProviders();
        Map<String, List<String>> roomHierarchy = roomToProvidersMap.get(roomName);

        if (roomHierarchy != null) {
            providerHierarchyCache.putAll(roomHierarchy);
        }
    }

    /**
     * Pre-identifies cabinets that have power providers with consumers using optimized registry lookups
     * This avoids creating temporary CabinetDiagramContext objects just for validation
     */
    private void identifyCabinetsWithProviders() {
        // If there are no cabinets, we can return early
        if (cabinetNames.isEmpty()) {
            return;
        }

        for (String cabinetName : cabinetNames) {
            List<String> providers = registry.getCabinetToPowerProviders().getOrDefault(cabinetName, Collections.emptyList());
            if (!providers.isEmpty()) {
                cabinetsWithProviders.add(cabinetName);
            }
        }
    }

    /**
     * Gets all cabinet contexts for this room
     * @return List of CabinetDiagramContext objects
     */
    public List<CabinetDiagramContext> getAllCabinetContexts() {
        List<CabinetDiagramContext> contexts = new ArrayList<>();

        for (String cabinetName : cabinetNames) {
            CabinetDiagramContext context = getCabinetContext(cabinetName);
            if (context != null) {
                contexts.add(context);
            }
        }

        return contexts;
    }

    /**
     * Gets a CabinetDiagramContext for a specific cabinet in the room
     * This reuses the pre-cached properties for efficiency
     *
     * @param cabinetName The name of the cabinet
     * @return A CabinetDiagramContext for the cabinet, or null if not found
     */
    public CabinetDiagramContext getCabinetContext(String cabinetName) {
        // Check if we already have a context for this cabinet
        if (cabinetContexts.containsKey(cabinetName)) {
            return cabinetContexts.get(cabinetName);
        }

        try {
            // Use cached cabinet block instead of looking it up again
            Class cabinetBlock = registry.getCabinetBlockByName(cabinetName);
            if (cabinetBlock != null) {
                // Get properties from the global cache
                Map<String, Property> properties = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinetName);

                // Create context with pre-cached properties
                CabinetDiagramContext context = new CabinetDiagramContext(
                        project, cabinetBlock, registry, true, roomName, properties);

                // Store in cache
                cabinetContexts.put(cabinetName, context);
                return context;
            } else {
                Log("Warning: Cabinet block not found in cache for " + cabinetName + " in room " + roomName);
            }
        } catch (Exception e) {
            Log("Error creating cabinet context for " + cabinetName + ": " + e.getMessage());
        }

        return null;
    }

    /**
     * @return The MagicDraw project
     */
    public Project getProject() {
        return project;
    }

    /**
     * @return Number of cabinets in this room
     */
    public int getCabinetCount() {
        return cabinetNames.size();
    }

    /**
     * @return Whether this room has any cabinets with power providers that have consumers
     */
    public boolean hasCabinetsWithProviders() {
        return !cabinetsWithProviders.isEmpty();
    }

    /**
     * Adds cabinets and room-level power providers to the room diagram
     *
     * @param diagram The diagram to add elements to
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    public void addToRoomDiagram(DiagramPresentationElement diagram) throws ReadOnlyElementException {
        if (diagram == null) {
            Log("Error: Cannot add elements to null diagram");
            return;
        }

        // Add room-level power elements (providers and their consumers) first
        addPowerElementsToRoomDiagram(diagram);

        // Then add cabinets with providers
        addCabinetsWithProvidersToRoomDiagram(diagram);
    }

    /**
     * Adds room-level power elements (providers with their hierarchy and associated consumers) to the diagram
     * Gets both power providers and power consumers from getRoomNonCabinetProperties() and adds providers
     * along with any consumers connected to those providers found in the room
     *
     * @param diagram The diagram to add power elements to
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addPowerElementsToRoomDiagram(DiagramPresentationElement diagram) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Get all non-cabinet properties once (includes both providers and consumers)
        Map<String, Property> allNonCabinetProps = Utilities.getRoomNonCabinetProperties(roomName);

        // Get room-level power provider properties, passing the already-retrieved non-cabinet properties
        Map<String, Property> roomProviderProps = getRoomPowerProviderProperties(allNonCabinetProps);

        // Add room-level providers to diagram
        for (Map.Entry<String, Property> entry : roomProviderProps.entrySet()) {
            String providerName = entry.getKey();
            Property providerProperty = entry.getValue();

            try {
                PresentationElement providerPE = manager.createShapeElement(providerProperty, diagram, true);

                // Add child providers under this parent using cached hierarchy
                addChildProvidersToParent(diagram, providerPE, providerName, providerProperty);

                // Add consumers connected to this provider that are found in the room
                addConsumersForProvider(diagram, providerName, allNonCabinetProps);

            } catch (Exception e) {
                Log("Error adding room-level provider " + providerName + " to diagram: " + e.getMessage());
            }
        }
    }

    /**
     * Gets room-level power provider properties from the room element cache
     *
     * @return Map of provider name to Property object for room-level providers
     */
    private Map<String, Property> getRoomPowerProviderProperties() {
        // Get power properties for this specific room
        Map<String, Property> nonCabinetProps = Utilities.getRoomNonCabinetProperties(roomName);
        return getRoomPowerProviderProperties(nonCabinetProps);
    }

    /**
     * Gets room-level power provider properties from the room element cache
     * Optimized version that uses pre-retrieved non-cabinet properties
     *
     * @param nonCabinetProps Pre-retrieved non-cabinet properties for the room
     * @return Map of provider name to Property object for room-level providers
     */
    private Map<String, Property> getRoomPowerProviderProperties(Map<String, Property> nonCabinetProps) {
        // Use cached data if available
        if (!roomPowerProviderPropertiesCache.isEmpty()) {
            return new HashMap<>(roomPowerProviderPropertiesCache);
        }

        Map<String, Property> roomProviderProps = new HashMap<>();

        // Get parent providers from cached hierarchy
        Set<String> roomParentProviders = providerHierarchyCache.keySet();

        if (roomParentProviders.isEmpty()) {
            return roomProviderProps;
        }

        // Filter for power providers that are parent providers in this room
        for (Map.Entry<String, Property> entry : nonCabinetProps.entrySet()) {
            String elementName = entry.getKey();
            if (roomParentProviders.contains(elementName)) {
                roomProviderProps.put(elementName, entry.getValue());
            }
        }

        // Cache the results for future use
        roomPowerProviderPropertiesCache.putAll(roomProviderProps);

        return roomProviderProps;
    }

    /**
     * Adds child power providers under their parent provider
     * OPTIMIZED: Uses pre-retrieved parent provider property to avoid redundant lookups
     *
     * @param diagram The diagram
     * @param parentPE The parent provider presentation element
     * @param parentProviderName The parent provider name
     * @param parentProviderProperty The parent provider property (already retrieved)
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addChildProvidersToParent(DiagramPresentationElement diagram, PresentationElement parentPE,
                                           String parentProviderName, Property parentProviderProperty) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Use cached hierarchy data instead of fetching from registry again
        List<String> childProviders = providerHierarchyCache.get(parentProviderName);

        if (childProviders != null && !childProviders.isEmpty()) {
            for (String childProviderName : childProviders) {
                try {
                    // Get the parent provider block to find child part properties
                    Set<String> allProviders = registry.getPowerProviders();
                    if (allProviders.contains(parentProviderName)) {
                        // Use the already-retrieved parent provider property
                        if (parentProviderProperty != null && parentProviderProperty.getType() instanceof Class) {
                            Class parentProviderBlock = (Class) parentProviderProperty.getType();

                            // Find child provider part property in parent provider block
                            Property childProviderProperty = Utilities.ModelElements.findPropertyByName(parentProviderBlock, childProviderName);

                            if (childProviderProperty != null) {
                                manager.createShapeElement(childProviderProperty, parentPE, true);
                            }
                        }
                    }
                } catch (Exception e) {
                    Log("Error adding power provider " + childProviderName + " under parent " + parentProviderName + ": " + e.getMessage());
                }
            }
        }
    }

    /**
     * Adds power consumers connected to a specific provider to the diagram
     * Only adds consumers that are found in the room's non-cabinet properties
     *
     * @param diagram The diagram to add consumers to
     * @param providerName The provider name to find consumers for
     * @param allNonCabinetProps All non-cabinet properties in the room (includes consumers)
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addConsumersForProvider(DiagramPresentationElement diagram, String providerName,
                                       Map<String, Property> allNonCabinetProps) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Get consumers connected to this provider from the registry
        List<String> connectedConsumers = registry.getConsumersForProvider(providerName);

        // Add consumers that are found in the room's non-cabinet properties
        for (String consumerName : connectedConsumers) {
            Property consumerProperty = allNonCabinetProps.get(consumerName);
            if (consumerProperty != null) {
                try {
                    manager.createShapeElement(consumerProperty, diagram, true);
                } catch (Exception e) {
                    Log("Error adding consumer " + consumerName + " for provider " + providerName + " to diagram: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Finds the cabinet that hosts a specific power provider
     *
     * @param providerName The provider name
     * @return The cabinet name or null if not found
     */
    private String findCabinetForProvider(String providerName) {
        Map<String, List<String>> cabinetProviders = registry.getCabinetToPowerProviders();

        for (Map.Entry<String, List<String>> entry : cabinetProviders.entrySet()) {
            String cabinetName = entry.getKey();
            List<String> providers = entry.getValue();

            if (providers.contains(providerName)) {
                return cabinetName;
            }
        }

        return null;
    }

    /**
     * Adds cabinets with power providers to the room diagram
     * Only adds cabinets that have power providers with consumers
     *
     * @param diagram The diagram to add cabinets to
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addCabinetsWithProvidersToRoomDiagram(DiagramPresentationElement diagram) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Use the pre-identified cabinets with providers
        for (String cabinetName : cabinetsWithProviders) {
            try {
                // Add cabinet shape - get cabinet property from room's cabinet properties
                Map<String, Property> roomCabinetProps = Utilities.getRoomCabinetProperties(roomName);
                Property cabinetProperty = roomCabinetProps.get(cabinetName);
                CabinetDiagramContext cabinetContext = getCabinetContext(cabinetName);

                if (cabinetProperty != null && cabinetContext != null) {
                    PresentationElement cabinetPE = manager.createShapeElement(cabinetProperty, diagram, true);
                    List<String> providers = cabinetContext.getProviders();
                    for (String providerName : providers) {
                        Property providerProperty = cabinetContext.getPartProperty(providerName);
                        if (providerProperty != null) {
                            manager.createShapeElement(providerProperty, cabinetPE, true);
                        }
                    }
                }
            } catch (Exception e) {
                Log("Error adding cabinet " + cabinetName + " to diagram: " + e.getMessage());
                // Continue with other cabinets
            }
        }
    }
}



